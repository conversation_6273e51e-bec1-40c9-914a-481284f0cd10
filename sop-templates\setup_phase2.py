#!/usr/bin/env python3
"""
Phase 2 Setup Script - Enhanced PDF Generator & Automation Pipeline
Installs dependencies and configures the system for Phase 2 features
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, description=""):
    """Run a command and handle errors"""
    try:
        logger.info(f"Running: {description or command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {command}")
        logger.error(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro} ✅")
    return True


def setup_virtual_environment():
    """Setup virtual environment if it doesn't exist"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        logger.info("Virtual environment already exists ✅")
        return True
    
    logger.info("Creating virtual environment...")
    if not run_command("python -m venv venv", "Creating virtual environment"):
        return False
    
    logger.info("Virtual environment created ✅")
    return True


def install_dependencies():
    """Install required dependencies for Phase 2"""
    logger.info("Installing Phase 2 dependencies...")
    
    # Determine pip command based on OS
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_cmd = "venv/bin/pip"
    
    # Core dependencies for Phase 2
    dependencies = [
        "reportlab>=4.0.0",  # Enhanced PDF generation
        "qrcode[pil]>=7.4.0",  # QR code generation
        "Pillow>=10.0.0",  # Image processing
        "markdown>=3.5.0",  # Markdown processing
        "schedule>=1.2.0",  # Task scheduling
        "requests>=2.31.0",  # HTTP requests for notifications
        "python-dotenv>=1.0.0",  # Environment variables
        "tqdm>=4.66.0",  # Progress bars
        "jinja2>=3.1.0",  # Template rendering
        "pyyaml>=6.0.0",  # YAML processing
        "openai>=1.0.0",  # OpenAI API (optional)
        "groq>=0.4.0",  # Groq API (optional)
        "httpx>=0.25.0",  # Async HTTP client
    ]
    
    # Install each dependency
    for dep in dependencies:
        if not run_command(f"{pip_cmd} install {dep}", f"Installing {dep}"):
            logger.warning(f"Failed to install {dep}, continuing...")
    
    # Install additional PDF dependencies
    pdf_deps = [
        "weasyprint",  # Alternative PDF generator
        "beautifulsoup4",  # HTML parsing
        "lxml",  # XML processing
    ]
    
    for dep in pdf_deps:
        if not run_command(f"{pip_cmd} install {dep}", f"Installing {dep}"):
            logger.warning(f"Failed to install {dep} (optional), continuing...")
    
    logger.info("Dependencies installation completed ✅")
    return True


def create_directory_structure():
    """Create required directory structure for Phase 2"""
    directories = [
        "outputs/templates",
        "outputs/pdfs", 
        "outputs/reports",
        "outputs/staging",
        "logs",
        "cache",
        "designs/assets",
        "config",
    ]
    
    logger.info("Creating directory structure...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created: {directory}")
    
    logger.info("Directory structure created ✅")
    return True


def create_environment_file():
    """Create .env file with Phase 2 configuration"""
    env_file = Path(".env")
    
    if env_file.exists():
        logger.info(".env file already exists ✅")
        return True
    
    logger.info("Creating .env file...")
    
    env_content = """# SOP Builder MVP - Phase 2 Configuration

# LLM API Configuration (Free Providers)
OPENROUTER_API_KEY=your_openrouter_api_key_here
GROQ_API_KEY=your_groq_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
TOGETHER_API_KEY=your_together_api_key_here

# OpenAI (Optional - Paid)
OPENAI_API_KEY=your_openai_api_key_here

# Cache Configuration
CACHE_DURATION_HOURS=24
USE_HARDCODED_CONTENT=False

# API Configuration
MAX_API_RETRIES=3
API_TIMEOUT=30

# Storage Configuration
LOCAL_STORAGE_PATH=./outputs

# Email Notifications (Optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# Slack Notifications (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Google Sheets Integration (Optional)
GOOGLE_SHEETS_CREDENTIALS_PATH=./config/google_credentials.json
COMPLIANCE_SHEET_ID=your_google_sheet_id

# Debug Configuration
DEBUG=False
LOG_LEVEL=INFO

# PDF Generation
PDF_QUALITY=high
INCLUDE_QR_CODES=True

# Automation
ENABLE_SCHEDULER=True
PARALLEL_GENERATION=True
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    logger.info(".env file created ✅")
    logger.info("Please edit .env file with your actual API keys and configuration")
    return True


def create_sample_brand_config():
    """Create sample brand configuration"""
    config_file = Path("config/brand_config.json")
    config_file.parent.mkdir(exist_ok=True)
    
    if config_file.exists():
        logger.info("Brand config already exists ✅")
        return True
    
    brand_config = {
        "primary_color": "#2C3E50",
        "secondary_color": "#3498DB", 
        "accent_color": "#E74C3C",
        "success_color": "#27AE60",
        "warning_color": "#F39C12",
        "logo_path": "designs/assets/logo.png",
        "font_family": "Helvetica",
        "company_name": "Your Company Name",
        "tagline": "Professional SOP Solutions",
        "footer_text": "Generated with AI-Enhanced SOP Builder"
    }
    
    import json
    with open(config_file, 'w') as f:
        json.dump(brand_config, f, indent=2)
    
    logger.info("Sample brand config created ✅")
    return True


def run_initial_test():
    """Run initial test to verify setup"""
    logger.info("Running initial setup test...")
    
    try:
        # Test imports
        sys.path.append('scripts/generators')
        sys.path.append('scripts/utils')
        
        from sop_generator import SOPGenerator
        from pdf_generator import EnhancedSOPPDFGenerator
        
        logger.info("Core modules imported successfully ✅")
        
        # Test basic functionality
        generator = SOPGenerator('restaurant')
        logger.info("SOP Generator initialized ✅")
        
        pdf_gen = EnhancedSOPPDFGenerator()
        logger.info("Enhanced PDF Generator initialized ✅")
        
        logger.info("Initial test completed successfully ✅")
        return True
        
    except Exception as e:
        logger.error(f"Initial test failed: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 SOP Builder MVP - Phase 2 Setup")
    print("Enhanced PDF Generator & Automation Pipeline")
    print("=" * 60)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Setting up virtual environment", setup_virtual_environment),
        ("Installing dependencies", install_dependencies),
        ("Creating directory structure", create_directory_structure),
        ("Creating environment file", create_environment_file),
        ("Creating brand configuration", create_sample_brand_config),
        ("Running initial test", run_initial_test),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if step_func():
                print(f"✅ {step_name} completed")
            else:
                print(f"❌ {step_name} failed")
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with error: {e}")
            failed_steps.append(step_name)
    
    print("\n" + "=" * 60)
    print("🎯 Setup Summary")
    
    if not failed_steps:
        print("🎉 Phase 2 setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Edit .env file with your API keys")
        print("2. Run: python test_enhanced_pdf.py")
        print("3. Test automation: python scripts/automation/pipeline_manager.py --generate restaurant")
        print("4. Start scheduler: python scripts/automation/pipeline_manager.py --scheduler")
        return True
    else:
        print(f"⚠️ Setup completed with {len(failed_steps)} issues:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease resolve these issues before proceeding.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
