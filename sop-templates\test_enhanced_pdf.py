#!/usr/bin/env python3
"""
Test script for Enhanced PDF Generator - Phase 2
Tests the enhanced PDF generator with AI-generated content
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add scripts to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts', 'generators'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts', 'utils'))

from sop_generator import SOPGenerator
from pdf_generator import EnhancedSOPPDFGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_enhanced_pdf_generation():
    """Test the enhanced PDF generator with AI-generated content"""
    print("🧪 Testing Enhanced PDF Generator - Phase 2")
    print("=" * 60)
    
    try:
        # Step 1: Generate SOP content using AI
        print("\n📝 Step 1: Generating SOP content...")
        generator = SOPGenerator('restaurant')
        template_content = generator.generate_template()
        
        # Print generation stats
        stats = template_content.get('generation_stats', {})
        print(f"✅ Generated {stats.get('successful_sections', 0)}/{stats.get('total_sections', 0)} sections")
        print(f"⚡ Generation time: {stats.get('generation_time_seconds', 0):.2f}s")
        print(f"💾 Cached sections: {stats.get('cached_sections', 0)}")
        
        # Step 2: Save template for reference
        print("\n💾 Step 2: Saving template...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        template_path = f"outputs/templates/test_restaurant_{timestamp}.json"
        os.makedirs(os.path.dirname(template_path), exist_ok=True)
        
        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(template_content, f, indent=2, ensure_ascii=False)
        
        template_size = os.path.getsize(template_path)
        print(f"✅ Template saved: {template_path}")
        print(f"📊 Template size: {template_size:,} bytes ({template_size/1024:.1f} KB)")
        
        # Step 3: Generate enhanced PDF
        print("\n🎨 Step 3: Generating enhanced PDF...")
        pdf_generator = EnhancedSOPPDFGenerator()
        pdf_path = f"outputs/pdfs/test_restaurant_{timestamp}.pdf"
        os.makedirs(os.path.dirname(pdf_path), exist_ok=True)
        
        generated_pdf = pdf_generator.generate_enhanced_pdf(template_content, pdf_path)
        
        pdf_size = os.path.getsize(generated_pdf)
        print(f"✅ PDF generated: {generated_pdf}")
        print(f"📊 PDF size: {pdf_size:,} bytes ({pdf_size/1024:.1f} KB)")
        
        # Step 4: Analyze content
        print("\n📋 Step 4: Content Analysis...")
        sections = template_content.get('sections', {})
        print(f"📄 Total sections: {len(sections)}")
        
        for section_name, section_data in sections.items():
            content_length = len(section_data.get('content', ''))
            status = "✅" if not section_data.get('error') else "⚠️"
            cached = "💾" if section_data.get('cached', False) else "🆕"
            print(f"  {status} {cached} {section_name}: {content_length:,} characters")
        
        # Step 5: Test with different content types
        print("\n🔬 Step 5: Testing markdown processing...")
        test_markdown = """
# Test Section

## Overview
This is a **test section** with various markdown elements.

### Key Points
- Bullet point 1
- Bullet point 2 with *emphasis*
- Bullet point 3

### Numbered List
1. First item
2. Second item
3. Third item

**Important:** This is an important note.

**Warning:** This is a warning message.

**Best Practice:** This is a best practice tip.

```
Code block example
with multiple lines
```

Regular paragraph with some text.
        """
        
        # Create test template
        test_template = {
            'metadata': {
                'type': 'test',
                'version': '1.0',
                'generated_date': datetime.now().isoformat(),
                'compliance_standards': ['Test Standard'],
                'generation_method': 'test'
            },
            'sections': {
                'Test Section': {
                    'content': test_markdown,
                    'order': 1,
                    'required': True,
                    'generated_at': datetime.now().isoformat(),
                    'cached': False
                }
            },
            'generation_stats': {
                'total_sections': 1,
                'successful_sections': 1,
                'failed_sections': 0,
                'cached_sections': 0,
                'generation_time_seconds': 0.1
            },
            'compliance_features': {
                'audit_trail': {'enabled': True},
                'version_control': {'enabled': True},
                'regulatory_links': {
                    'Test Regulation': 'https://example.com/test-regulation'
                }
            }
        }
        
        # Generate test PDF
        test_pdf_path = f"outputs/pdfs/test_markdown_{timestamp}.pdf"
        test_pdf = pdf_generator.generate_enhanced_pdf(test_template, test_pdf_path)
        
        test_pdf_size = os.path.getsize(test_pdf)
        print(f"✅ Test PDF generated: {test_pdf}")
        print(f"📊 Test PDF size: {test_pdf_size:,} bytes ({test_pdf_size/1024:.1f} KB)")
        
        # Step 6: Summary
        print("\n🎉 Test Summary:")
        print("=" * 40)
        print(f"✅ SOP Generation: Success")
        print(f"✅ Template Saving: Success")
        print(f"✅ Enhanced PDF: Success")
        print(f"✅ Markdown Test: Success")
        print(f"📁 Files created:")
        print(f"  - Template: {template_path}")
        print(f"  - Main PDF: {generated_pdf}")
        print(f"  - Test PDF: {test_pdf}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        logger.error(f"Test failed: {e}", exc_info=True)
        return False


def test_pipeline_integration():
    """Test integration with the automation pipeline"""
    print("\n🔗 Testing Pipeline Integration...")
    
    try:
        # Import pipeline manager
        sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts', 'automation'))
        from pipeline_manager import AutomationPipelineManager
        
        # Initialize pipeline
        pipeline = AutomationPipelineManager()
        
        # Test single template generation
        print("🧪 Testing single template generation...")
        result = pipeline.generate_single_template('restaurant', force_regenerate=False)
        
        if result['status'] == 'success':
            print(f"✅ Pipeline test successful!")
            print(f"📁 Template: {result['template_path']}")
            print(f"📁 PDF: {result['pdf_path']}")
            print(f"⏱️ Time: {result['generation_time']:.2f}s")
            return True
        else:
            print(f"❌ Pipeline test failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        logger.error(f"Pipeline test failed: {e}", exc_info=True)
        return False


def main():
    """Main test execution"""
    print("🚀 SOP Builder MVP - Phase 2 Testing")
    print("Enhanced PDF Generator & Automation Pipeline")
    print("=" * 60)
    
    # Ensure output directories exist
    os.makedirs('outputs/templates', exist_ok=True)
    os.makedirs('outputs/pdfs', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Enhanced PDF Generation
    print("\n🧪 Test 1: Enhanced PDF Generation")
    if test_enhanced_pdf_generation():
        tests_passed += 1
        print("✅ Test 1 PASSED")
    else:
        print("❌ Test 1 FAILED")
    
    # Test 2: Pipeline Integration
    print("\n🧪 Test 2: Pipeline Integration")
    if test_pipeline_integration():
        tests_passed += 1
        print("✅ Test 2 PASSED")
    else:
        print("❌ Test 2 FAILED")
    
    # Final results
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Phase 2 implementation is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Set up environment variables for notifications")
        print("2. Configure scheduling for automated runs")
        print("3. Deploy to production environment")
        print("4. Monitor system performance")
        return True
    else:
        print("⚠️ Some tests failed. Please check the logs and fix issues.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
